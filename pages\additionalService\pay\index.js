import additionalServiceApi from '../../../api/modules/additionalService.js';
import payApi from '../../../api/modules/pay.js';
import Session from '../../../common/Session.js';
import utils from '../../utils/util.js';

Page({
  data: {
    userInfo: null,
    orderDetailId: null,
    additionalServiceId: null,

    // 追加服务详情
    serviceDetail: null,
    loading: true,
    paying: false,

    // 模态框
    showModal: false,
    modalTitle: '',
    modalContent: '',
    modalButtons: [],
  },

  onLoad(options) {
    const { orderDetailId, id } = options;
    const userInfo = Session.getUser();

    if (!userInfo) {
      wx.showToast({
        title: '请先登录',
        icon: 'none',
      });
      wx.navigateBack();
      return;
    }

    this.setData({
      userInfo,
      orderDetailId: parseInt(orderDetailId),
      additionalServiceId: parseInt(id),
    });

    this.loadServiceDetail();
  },

  /**
   * 加载追加服务详情
   */
  async loadServiceDetail() {
    try {
      this.setData({ loading: true });

      const { orderDetailId, additionalServiceId } = this.data;
      const detail = await additionalServiceApi.detail(orderDetailId, additionalServiceId);

      if (detail) {
        // 检查状态是否可以支付
        if (detail.status !== 'confirmed') {
          wx.showToast({
            title: '当前状态不支持支付',
            icon: 'none',
          });
          wx.navigateBack();
          return;
        }

        // 格式化数据
        const formattedDetail = {
          ...detail,
          createdAt: detail.createdAt ? utils.formatNormalDate(detail.createdAt) : '',
          confirmTime: detail.confirmTime ? utils.formatNormalDate(detail.confirmTime) : '',
        };

        this.setData({ serviceDetail: formattedDetail });
      } else {
        // detail为null说明API返回了业务错误（如400），analysisRes已经显示了错误toast
        // 没有加载到任何信息时，延迟后返回到订单详情页面
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      }
    } catch (error) {
      // 只有真正的网络错误或其他异常才返回上一页
      console.error('加载追加服务详情失败:', error);
      wx.showToast({
        title: '网络错误，请重试',
        icon: 'none',
      });
      // 网络错误时才返回上一页
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 确认支付
   */
  confirmPay() {
    const { serviceDetail } = this.data;

    if (!serviceDetail) return;

    this.setData({
      showModal: true,
      modalTitle: '确认支付',
      modalContent: `确认支付 ¥${serviceDetail.totalFee} 吗？`,
      modalButtons: [
        {
          text: '取消',
          type: 'cancel',
          event: 'handleModalCancel',
        },
        {
          text: '确认支付',
          type: 'primary',
          event: 'handlePayConfirm',
        },
      ],
    });
  },

  /**
   * 处理支付确认
   */
  async handlePayConfirm() {
    this.setData({ showModal: false });
    await this.processPay();
  },

  /**
   * 处理支付
   */
  async processPay() {
    const { userInfo, orderDetailId, additionalServiceId, serviceDetail } = this.data;

    if (this.data.paying) return;

    try {
      this.setData({ paying: true });

      // 处理0元订单
      if (Number(serviceDetail.totalFee) === 0) {
        wx.showLoading({ title: '处理中...' });

        const result = await additionalServiceApi.pay(orderDetailId, additionalServiceId, userInfo.id);

        wx.hideLoading();

        if (result) {
          this.showPaymentSuccessModal();
        } else {
          wx.showToast({
            title: '支付失败，请重试',
            icon: 'none',
          });
        }
        return;
      }

      // 正常支付流程 - 复用主流程的微信支付
      wx.showLoading({ title: '发起支付...' });

      // 调用支付接口获取订单号
      const payResult = await additionalServiceApi.pay(orderDetailId, additionalServiceId, userInfo.id);

      wx.hideLoading();

      if (!payResult || !payResult.sn) {
        wx.showToast({
          title: '获取支付信息失败，请重试',
          icon: 'none',
        });
        return;
      }

      // 使用主流程的微信支付
      const _this = this;
      payApi.doPay({
        sn: payResult.sn,
        onOk: () => {
          // 支付成功后，调用支付成功接口更新订单状态
          additionalServiceApi.confirmPayment(orderDetailId, additionalServiceId, userInfo.id).then(() => {
            _this.showPaymentSuccessModal();
          }).catch((error) => {
            console.error('支付确认失败:', error);
            // 即使确认失败，也显示支付成功，因为微信支付已经成功
            _this.showPaymentSuccessModal();
          });
        },
        onCancel: () => {
          wx.showToast({
            title: '取消支付',
            icon: 'none',
          });
        },
        onError: () => {
          wx.showToast({
            title: '支付失败',
            icon: 'none',
          });
        },
        complete: () => {},
      });

    } catch (error) {
      wx.hideLoading();
      console.error('支付失败:', error);
      wx.showToast({
        title: '支付失败，请重试',
        icon: 'none',
      });
    } finally {
      this.setData({ paying: false });
    }
  },

  /**
   * 显示支付成功模态框
   */
  showPaymentSuccessModal() {
    this.setData({
      showModal: true,
      modalTitle: '支付成功',
      modalContent: '您的追加服务已支付成功，服务人员将为您提供服务',
      modalButtons: [
        {
          text: '确定',
          type: 'primary',
          event: 'handlePaymentSuccess',
        },
      ],
    });
  },

  /**
   * 处理支付成功
   */
  handlePaymentSuccess() {
    this.setData({ showModal: false });

    // 返回到订单详情页面并刷新，让用户看到最新的追加服务状态
    // 根据记忆，用户希望支付成功后返回到订单详情页面
    wx.navigateBack({
      delta: 3, // 返回到订单详情页面（跳过详情页和列表页）
    });
  },

  /**
   * 查看服务详情
   */
  viewServiceDetail() {
    const { orderDetailId, additionalServiceId } = this.data;

    wx.navigateTo({
      url: `/pages/additionalService/detail/index?orderDetailId=${orderDetailId}&id=${additionalServiceId}`,
    });
  },

  /**
   * 模态框取消
   */
  handleModalCancel() {
    this.setData({ showModal: false });
  },
});
