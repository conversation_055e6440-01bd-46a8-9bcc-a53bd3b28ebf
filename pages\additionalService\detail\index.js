import additionalServiceApi from '../../../api/modules/additionalService.js';
import Session from '../../../common/Session.js';
import utils from '../../utils/util.js';

Page({
  data: {
    userInfo: null,
    orderDetailId: null,
    additionalServiceId: null,

    // 追加服务详情
    serviceDetail: null,
    loading: true,

    // 状态映射
    statusMap: {
      pending_confirm: {
        text: '待确认',
        color: '#ff9500',
        desc: '您的追加服务申请已提交，等待员工确认',
        icon: '⏳',
      },
      confirmed: {
        text: '已确认',
        color: '#007aff',
        desc: '员工已确认您的申请，请尽快完成支付',
        icon: '✅',
      },
      paid: {
        text: '已付款',
        color: '#34c759',
        desc: '支付成功，服务进行中',
        icon: '💰',
      },
      rejected: {
        text: '已拒绝',
        color: '#ff3b30',
        desc: '很抱歉，您的申请被拒绝',
        icon: '❌',
      },
    },

    // 模态框
    showModal: false,
    modalTitle: '',
    modalContent: '',
    modalButtons: [],
  },

  onLoad(options) {
    const { orderDetailId, id } = options;
    const userInfo = Session.getUser();

    if (!userInfo) {
      wx.showToast({
        title: '请先登录',
        icon: 'none',
      });
      wx.navigateBack();
      return;
    }

    this.setData({
      userInfo,
      orderDetailId: parseInt(orderDetailId),
      additionalServiceId: parseInt(id),
    });

    this.loadServiceDetail();
  },

  onShow() {
    // 页面显示时刷新数据
    this.loadServiceDetail();
  },

  /**
   * 加载追加服务详情
   */
  async loadServiceDetail() {
    try {
      this.setData({ loading: true });

      const { orderDetailId, additionalServiceId } = this.data;
      const detail = await additionalServiceApi.detail(orderDetailId, additionalServiceId);

      if (detail) {
        // 格式化数据
        const statusInfo = this.data.statusMap[detail.status] || {
          text: detail.status,
          color: '#999',
          desc: '',
          icon: '❓',
        };

        // 如果是拒绝状态，优先使用rejectReason字段作为描述
        if (detail.status === 'rejected' && detail.rejectReason) {
          statusInfo.desc = detail.rejectReason;
        }

        const formattedDetail = {
          ...detail,
          createdAt: detail.createdAt ? utils.formatNormalDate(detail.createdAt) : '',
          confirmTime: detail.confirmTime ? utils.formatNormalDate(detail.confirmTime) : '',
          statusInfo: statusInfo,
        };

        this.setData({ serviceDetail: formattedDetail });
      } else {
        // detail为null说明API返回了业务错误（如400），analysisRes已经显示了错误toast
        // 没有加载到任何信息时，延迟后返回到订单详情页面
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      }
    } catch (error) {
      // 只有真正的网络错误或其他异常才返回上一页
      console.error('加载追加服务详情失败:', error);
      wx.showToast({
        title: '网络错误，请重试',
        icon: 'none',
      });
      // 网络错误时才返回上一页
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 去支付
   */
  goPay() {
    const { orderDetailId, additionalServiceId } = this.data;

    wx.navigateTo({
      url: `/pages/additionalService/pay/index?orderDetailId=${orderDetailId}&id=${additionalServiceId}`,
    });
  },

  /**
   * 查看订单详情
   */
  viewOrderDetail() {
    const { serviceDetail } = this.data;

    if (serviceDetail && serviceDetail.orderDetail) {
      const orderData = JSON.stringify(serviceDetail.orderDetail.order);
      wx.navigateTo({
        url: `/pages/serviceOrder/orderDetail/index?data=${encodeURIComponent(orderData)}`,
      });
    }
  },

  /**
   * 联系客服
   */
  contactService() {
    this.setData({
      showModal: true,
      modalTitle: '联系客服',
      modalContent: '如有疑问，请联系客服：400-123-4567',
      modalButtons: [
        {
          text: '确定',
          type: 'primary',
          event: 'handleModalConfirm',
        },
      ],
    });
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    this.loadServiceDetail().finally(() => {
      wx.stopPullDownRefresh();
    });
  },

  /**
   * 模态框确认
   */
  handleModalConfirm() {
    this.setData({ showModal: false });
  },

  /**
   * 模态框取消
   */
  handleModalCancel() {
    this.setData({ showModal: false });
  },
});
